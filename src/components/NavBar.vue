<template>
  <nav class="navbar-banner">
    <div class="navbar-content">
      <img class="logo-img" src="../assets/vc-logo.png" alt="VCFOOD LOGO" />
      <ul class="nav-links">
        <li v-for="item in navItems" :key="item.path">
          <router-link :to="item.path" active-class="active">{{ t(item.label) }}</router-link>
        </li>
      </ul>
      <div class="navbar-actions">
        <button class="lang-btn" @click="toggleLang">{{ t('nav.lang') }}</button>
        <router-link to="/admin" class="admin-btn">{{ t('nav.admin') }}</router-link>
      </div>
    </div>
  </nav>
</template>
<script setup>
import { useI18n } from 'vue-i18n';
import { onMounted } from 'vue';

const { locale, t } = useI18n();

const navItems = [
  { path: '/', label: 'nav.home' },
  { path: '/about', label: 'nav.about' },
  { path: '/services', label: 'nav.services' },
  { path: '/news', label: 'nav.news' },
  { path: '/contact', label: 'nav.contact' }
];

// 从本地存储恢复语言设置
onMounted(() => {
  const savedLocale = localStorage.getItem('vcfood-locale');
  if (savedLocale && (savedLocale === 'zh-HK' || savedLocale === 'en')) {
    locale.value = savedLocale;
  }
});

function toggleLang() {
  const newLocale = locale.value === 'zh-HK' ? 'en' : 'zh-HK';
  locale.value = newLocale;
  // 保存到本地存储
  localStorage.setItem('vcfood-locale', newLocale);
}
</script>
<style scoped>
.navbar-banner {
  width: 100vw;
  background: linear-gradient(90deg, #FFD600 60%, #ff9800 100%);
  box-shadow: 0 2px 12px #f7e7a3;
  padding: 0;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 100;
}
.navbar-content {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 96px;
  padding: 0 32px;
}
.logo-img {
  height: 84px;
  width: auto;
  margin-right: 40px;
  filter: drop-shadow(0 2px 8px #ffe066);
}
.nav-links {
  display: flex;
  gap: 40px;
  list-style: none;
  margin: 0;
  padding: 0;
  flex-wrap: nowrap;
}
.nav-links a {
  color: #222;
  text-decoration: none;
  font-size: 22px;
  font-weight: 700;
  transition: color 0.2s;
  white-space: nowrap;
}
.nav-links a.active, .nav-links a:hover {
  color: #ff9800;
}
.navbar-actions {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 10px;
  margin-left: 32px;
}
.lang-btn {
  background: #fffbe6;
  border: none;
  color: #222;
  font-size: 14px;
  padding: 6px 18px;
  border-radius: 16px;
  cursor: pointer;
  font-weight: 500;
  transition: background 0.2s;
}
.lang-btn:hover {
  background: #ffe066;
}
.admin-btn {
  background: #ff9800;
  color: #fff;
  border-radius: 16px;
  padding: 6px 18px;
  font-size: 14px;
  font-weight: 500;
  text-decoration: none;
  transition: background 0.2s;
}
.admin-btn:hover {
  background: #FFD600;
  color: #222;
}
@media (max-width: 900px) {
  .navbar-content { flex-direction: column; height: auto; padding: 0 8px; gap: 8px; }
  .logo-img { margin: 0 0 8px 0; height: 40px; }
  .nav-links { gap: 12px; }
  .navbar-actions { margin-left: 0; align-items: center; }
}
</style> 