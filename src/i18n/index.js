import { createI18n } from 'vue-i18n'
import zhHK from './zh-HK.json'
import en from './en.json'

// 获取浏览器语言设置，默认为中文
const getDefaultLocale = () => {
  const browserLang = navigator.language || navigator.userLanguage
  if (browserLang.startsWith('en')) {
    return 'en'
  }
  return 'zh-HK'
}

const i18n = createI18n({
  legacy: false,
  locale: getDefaultLocale(),
  fallbackLocale: 'en',
  messages: {
    'zh-HK': zhHK,
    en
  },
  // 添加错误处理
  silentTranslationWarn: true,
  silentFallbackWarn: true
})

export default i18n