import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue';
import path from 'path'; // 新增路径处理

function pathResolve(dir) {
    return resolve(root, '.', dir)
}

// https://vite.dev/config/
export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
       '@': path.resolve(__dirname, 'src')
    } 
  },
  build: {
    rollupOptions: {
      output: {
        // 语言文件打包策略 (重要)
        manualChunks: (id) => {
          if (id.includes('/i18n/')) {
            // 将所有语言文件合并到独立chunk
            return 'locales';
          }
        }
      }
    }
  },
  base: './', // 确保相对路径正确
})
