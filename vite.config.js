import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue';
import path from 'path'; // 新增路径处理

// https://vite.dev/config/
export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
       '@': path.resolve(__dirname, 'src')
    }
  },
  build: {
    rollupOptions: {
      output: {
        // 语言文件打包策略 (重要)
        manualChunks: (id) => {
          if (id.includes('/i18n/')) {
            // 将所有语言文件合并到独立chunk
            return 'locales';
          }
        }
      }
    },
    // 确保资源文件正确处理
    assetsDir: 'assets',
    // 确保构建输出稳定
    sourcemap: false
  },
  base: './', // 确保相对路径正确
})
