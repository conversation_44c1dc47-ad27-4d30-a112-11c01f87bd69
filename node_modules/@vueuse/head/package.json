{"name": "@vueuse/head", "version": "2.0.0", "packageManager": "pnpm@8.7.5", "description": "Document head manager for Vue 3. SSR ready.", "author": {"name": "EGOIST", "url": "https://egoist.sh"}, "maintainers": [{"name": "<PERSON>", "url": "https://harlanzw.com"}], "license": "MIT", "repository": {"type": "git", "url": "https://github.com/vueuse/head"}, "keywords": ["vue", "head", "document", "ssr", "meta"], "publishConfig": {"access": "public"}, "sideEffects": false, "main": "dist/index.cjs", "module": "dist/index.mjs", "types": "dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "files": ["dist"], "scripts": {"stub": "unbuild --stub", "build": "unbuild", "play:vite": "vite examples/vite-ssr", "prepublishOnly": "npm run build", "test": "nuxi prepare examples/nuxt3 && vitest", "test:e2e": "vitest tests/e2e", "release": "kanpai", "lint": "eslint \"**/*.{ts,vue,json,yml,tsx}\" --fix"}, "peerDependencies": {"vue": ">=2.7 || >=3"}, "dependencies": {"@unhead/dom": "^1.7.0", "@unhead/schema": "^1.7.0", "@unhead/ssr": "^1.7.0", "@unhead/vue": "^1.7.0"}, "devDependencies": {"@antfu/eslint-config": "^0.41.3", "@nuxt/kit": "3.7.2", "@nuxt/test-utils": "3.7.2", "@vitejs/plugin-vue": "^4.3.4", "@vitejs/plugin-vue-jsx": "^3.0.2", "@vue/compiler-sfc": "^3.3.4", "@vue/server-renderer": "^3.3.4", "cheerio": "1.0.0-rc.12", "eslint": "^8.49.0", "execa": "^8.0.1", "get-port-please": "^3.1.1", "jsdom": "^22.1.0", "kanpai": "^0.11.0", "mlly": "^1.4.2", "nuxt": "^3.7.2", "pathe": "^1.1.1", "playwright": "^1.37.1", "typescript": "^5.2.2", "unbuild": "^2.0.0", "vite": "^4.4.9", "vitest": "^0.34.4", "vue": "^3.3.4", "vue-router": "^4.2.4"}, "resolutions": {"@vueuse/head": "link:."}}