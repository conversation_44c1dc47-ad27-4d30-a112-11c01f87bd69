#!/usr/bin/env node

import fs from 'fs';
import path from 'path';

console.log('🔍 检查构建文件...');

// 检查 dist 目录是否存在
if (!fs.existsSync('./dist')) {
  console.error('❌ dist 目录不存在，请先运行 npm run build');
  process.exit(1);
}

// 检查 index.html
const indexPath = './dist/index.html';
if (!fs.existsSync(indexPath)) {
  console.error('❌ index.html 不存在');
  process.exit(1);
}

const indexContent = fs.readFileSync(indexPath, 'utf-8');
console.log('✅ index.html 存在');

// 检查是否包含语言文件引用
if (indexContent.includes('locales-')) {
  console.log('✅ 语言文件已正确引用');
} else {
  console.warn('⚠️  未找到语言文件引用');
}

// 检查 assets 目录
const assetsDir = './dist/assets';
if (!fs.existsSync(assetsDir)) {
  console.error('❌ assets 目录不存在');
  process.exit(1);
}

// 查找语言文件
const files = fs.readdirSync(assetsDir);
const localeFile = files.find(file => file.startsWith('locales-') && file.endsWith('.js'));

if (localeFile) {
  console.log(`✅ 找到语言文件: ${localeFile}`);
  
  // 检查语言文件内容
  const localeContent = fs.readFileSync(path.join(assetsDir, localeFile), 'utf-8');
  
  if (localeContent.includes('nav.home') && localeContent.includes('首頁')) {
    console.log('✅ 中文翻译存在');
  } else {
    console.warn('⚠️  中文翻译可能缺失');
  }
  
  if (localeContent.includes('nav.home') && localeContent.includes('Home')) {
    console.log('✅ 英文翻译存在');
  } else {
    console.warn('⚠️  英文翻译可能缺失');
  }
} else {
  console.error('❌ 未找到语言文件');
  process.exit(1);
}

console.log('🎉 构建检查完成！');
