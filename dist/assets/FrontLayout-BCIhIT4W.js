import{r as t,c as a,o as n,aj as s,R as p,x as e}from"./locales-B5peU6Va.js";import{N as m}from"./NavBar-OuIYKUdi.js";import{F as i}from"./FooterBar-BXyJdeGc.js";import"./index-ONkDcv6l.js";const l={__name:"GlobalMessage",setup(_){return(r,c)=>{const o=t("a-config-provider");return n(),a(o,{theme:{token:{colorPrimary:"#FFD600"}}},{default:s(()=>[p(r.$slots,"default")]),_:3})}}},F={__name:"FrontLayout",setup(_){return(r,c)=>{const o=t("router-view");return n(),a(l,null,{default:s(()=>[e(m),e(o),e(i)]),_:1})}}};export{F as default};
