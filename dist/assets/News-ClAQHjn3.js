import{_ as A}from"./carousel-1-BAepWELb.js";import{k as C}from"./marked.esm-DJbTXz-_.js";import{g as M}from"./news-C-lmIvy8.js";import{_ as x,a as u,u as L}from"./index-ONkDcv6l.js";import{ak as U,e as p,j as I,b as h,l as $,al as d,am as e,ao as t,u as l,F as B,an as D,o as _,aq as S,ar as T}from"./locales-B5peU6Va.js";import"./request-D1ZcfQrQ.js";const Z={class:"page-root"},z={class:"news-container animate__animated animate__fadeInUp"},E={class:"news-header"},F={class:"slogan"},H={class:"news-section"},P={class:"map-block"},V=["alt"],j={class:"news-list"},q=["onClick"],R={class:"news-title"},G={class:"news-date"},J={class:"news-content news-content-ellipsis"},K=["innerHTML"],O=["src","alt"],Q={class:"pagination"},W=["disabled"],X=["disabled"],w=5,Y={__name:"News",setup(ee){const{t:n}=U(),f=u.error;u.error=()=>{};const i=p([]),o=p(1),r=p(0);L(),I(v);async function v(){try{const s=await M({page:o.value,pageSize:w});Array.isArray(s.data)?(i.value=s.data,r.value=s.data.length):Array.isArray(s.data?.list)?(i.value=s.data.list,r.value=s.data.total||s.data.list.length):(i.value=[],r.value=0)}catch{i.value=[],r.value=0}}const m=h(()=>Math.max(1,Math.ceil(r.value/w))),k=h(()=>i.value);function y(s){return C.parse(s||"")}function b(s){window.open(`/news/${s.id}`,"_blank")}function g(s){o.value=s,v()}return $(()=>{u.error=f}),(s,c)=>(_(),d("div",Z,[e("div",z,[e("div",E,[e("h1",null,t(l(n)("news.title")),1),e("div",F,t(l(n)("news.slogan")),1)]),e("div",H,[e("div",P,[e("img",{src:A,alt:l(n)("contact.mapAlt"),class:"map-img"},null,8,V)]),e("h2",null,t(l(n)("news.listTitle")),1),e("ul",j,[(_(!0),d(B,null,D(k.value,(a,N)=>(_(),d("li",{key:a.id,class:"news-item animate__animated animate__fadeInUp",style:S({animationDelay:.2+N*.1+"s"}),onClick:se=>b(a)},[e("div",R,t(a.titleZh),1),e("div",G,t(a.createdAt?.substring(0,10)),1),e("div",J,[e("div",{innerHTML:y(a.contentZh)},null,8,K)]),a.imageUrl?(_(),d("img",{key:0,src:a.imageUrl,class:"news-img",alt:a.titleZh},null,8,O)):T("",!0)],12,q))),128))]),e("div",Q,[e("button",{disabled:o.value===1,onClick:c[0]||(c[0]=a=>g(o.value-1))},t(l(n)("news.prev")),9,W),e("span",null,t(l(n)("news.page",{page:o.value,total:m.value})),1),e("button",{disabled:o.value===m.value,onClick:c[1]||(c[1]=a=>g(o.value+1))},t(l(n)("news.next")),9,X)])])])]))}},re=x(Y,[["__scopeId","data-v-fe581941"]]);export{re as default};
