import{_ as n}from"./index-ONkDcv6l.js";import{ak as c,al as _,am as e,ao as r,u as s,x as d,aj as i,r as l,o as m,J as p}from"./locales-B5peU6Va.js";const u={class:"page-root"},f={class:"error-container animate__animated animate__fadeInUp"},h={class:"error-header"},k={class:"slogan"},v={class:"error-content"},x={__name:"Error500",setup(b){const{t:o}=c();return(g,t)=>{const a=l("router-link");return m(),_("div",u,[e("div",f,[e("div",h,[t[0]||(t[0]=e("h1",null,"500",-1)),e("div",k,r(s(o)("error.500")),1)]),e("div",v,[e("p",null,r(s(o)("error.500desc")),1),d(a,{to:"/",class:"back-home-btn"},{default:i(()=>[p(r(s(o)("error.backHome")),1)]),_:1})])])])}}},I=n(x,[["__scopeId","data-v-66a3d3c3"]]);export{I as default};
