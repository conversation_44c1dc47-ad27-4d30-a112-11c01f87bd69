import{_ as h}from"./index-DzBUZktP.js";import{ak as b,j as g,al as i,o as v,am as t,F as f,an as k,x as _,aj as u,J as d,ao as s,u as l,r as x}from"./locales-BE9hxia1.js";const C=""+new URL("vc-logo-CWH6HCP6.png",import.meta.url).href,L={class:"navbar-banner"},N={class:"navbar-content"},w={class:"nav-links"},B={class:"navbar-actions"},H={__name:"NavBar",setup(I){const{locale:e,t:n}=b(),p=[{path:"/",label:"nav.home"},{path:"/about",label:"nav.about"},{path:"/services",label:"nav.services"},{path:"/news",label:"nav.news"},{path:"/contact",label:"nav.contact"}];g(()=>{const a=localStorage.getItem("vcfood-locale");a&&(a==="zh-HK"||a==="en")&&(e.value=a)});function m(){const a=e.value==="zh-HK"?"en":"zh-HK";e.value=a,localStorage.setItem("vcfood-locale",a)}return(a,c)=>{const r=x("router-link");return v(),i("nav",L,[t("div",N,[c[0]||(c[0]=t("img",{class:"logo-img",src:C,alt:"VCFOOD LOGO"},null,-1)),t("ul",w,[(v(),i(f,null,k(p,o=>t("li",{key:o.path},[_(r,{to:o.path,"active-class":"active"},{default:u(()=>[d(s(l(n)(o.label)),1)]),_:2},1032,["to"])])),64))]),t("div",B,[t("button",{class:"lang-btn",onClick:m},s(l(n)("nav.lang")),1),_(r,{to:"/admin",class:"admin-btn"},{default:u(()=>[d(s(l(n)("nav.admin")),1)]),_:1})])])])}}},z=h(H,[["__scopeId","data-v-95e0fcdc"]]);export{z as N,C as _};
