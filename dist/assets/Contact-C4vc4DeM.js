import{_ as m}from"./index-ONkDcv6l.js";import{ak as r,al as e,am as t,ao as a,u as n,F as p,an as h,o as i,aq as v,ar as u,J as l}from"./locales-B5peU6Va.js";const f=""+new URL("map-BrWRbbno.png",import.meta.url).href,k={class:"page-root"},g={class:"contact-container animate__animated animate__fadeInUp"},y={class:"contact-header"},b={class:"slogan"},x={class:"contact-section"},B={class:"map-block"},I=["alt"],w={class:"contacts"},C={class:"contact-info"},N={class:"contact-name"},U={key:0,class:"contact-row"},V={class:"contact-row"},D={class:"contact-tip"},F={__name:"Contact",setup(L){const{t:o}=r(),_=[{name:"<PERSON><PERSON>",phone:"56309788",email:"<EMAIL>"},{name:"<PERSON>",phone:"84943394",email:"<EMAIL>"}];return(R,c)=>(i(),e("div",k,[t("div",g,[t("div",y,[t("h1",null,a(n(o)("contact.title")),1),t("div",b,a(n(o)("contact.slogan")),1)]),t("div",x,[t("div",B,[t("img",{src:f,alt:n(o)("contact.mapAlt"),class:"map-img"},null,8,I)]),t("h2",null,a(n(o)("contact.contactWay")),1),t("div",w,[(i(),e(p,null,h(_,(s,d)=>t("div",{class:"contact-card animate__animated animate__fadeInUp",key:s.name,style:v({animationDelay:.2+d*.1+"s"})},[t("div",C,[t("div",N,a(s.name),1),s.phone?(i(),e("div",U,[c[0]||(c[0]=t("span",{class:"contact-icon"},"📞",-1)),l(a(s.phone),1)])):u("",!0),t("div",V,[c[1]||(c[1]=t("span",{class:"contact-icon"},"✉️",-1)),l(a(s.email),1)])])],4)),64))]),t("div",D,a(n(o)("contact.tip")),1)])])]))}},W=m(F,[["__scopeId","data-v-3eb15529"]]);export{W as default};
