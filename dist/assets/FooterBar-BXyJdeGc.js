import{_ as c}from"./NavBar-OuIYKUdi.js";import{_}from"./index-ONkDcv6l.js";import{ak as u,al as f,o as d,am as o,x as r,aj as l,J as n,ao as e,u as s,r as m}from"./locales-B5peU6Va.js";const p={class:"footer-bar"},v={class:"footer-main"},g={class:"footer-links"},k={class:"footer-copy"},x={__name:"FooterBar",setup(B){const{t}=u();return(h,i)=>{const a=m("router-link");return d(),f("footer",p,[o("div",v,[i[0]||(i[0]=o("img",{class:"footer-logo-img",src:c,alt:"VCFOOD LOGO"},null,-1)),o("ul",g,[o("li",null,[r(a,{to:"/"},{default:l(()=>[n(e(s(t)("nav.home")),1)]),_:1})]),o("li",null,[r(a,{to:"/about"},{default:l(()=>[n(e(s(t)("nav.about")),1)]),_:1})]),o("li",null,[r(a,{to:"/services"},{default:l(()=>[n(e(s(t)("nav.services")),1)]),_:1})]),o("li",null,[r(a,{to:"/news"},{default:l(()=>[n(e(s(t)("nav.news")),1)]),_:1})]),o("li",null,[r(a,{to:"/contact"},{default:l(()=>[n(e(s(t)("nav.contact")),1)]),_:1})])])]),o("div",k,e(s(t)("footer.copyright"))+" | "+e(s(t)("footer.icp")),1),i[1]||(i[1]=o("div",{class:"footer-gradient"},null,-1))])}}},b=_(x,[["__scopeId","data-v-3590fa23"]]);export{b as F};
