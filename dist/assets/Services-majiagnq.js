import{_ as g,a as d,b as m,c as u,d as b,e as p,f,g as h,h as j,i as k,j as y,k as I,l as S,m as U,n as x,o as B,p as q,q as w}from"./vue-C4CxszQz.js";import{_ as D}from"./index-DzBUZktP.js";import{ak as F,b as L,al as a,am as s,ao as _,u as i,F as z,an as E,o,aq as N}from"./locales-BE9hxia1.js";const O={class:"page-root"},R={class:"services-container animate__animated animate__fadeInUp"},T={class:"services-header"},V={class:"slogan"},$={class:"services-section"},A={class:"service-cards"},C=["src","alt"],G={class:"service-title"},H={class:"service-desc"},J={__name:"Services",setup(K){const{t,tm:l}=F(),n=L(()=>l("services.list"));function v(c){return new URL(Object.assign({"../assets/404-illustration.svg":w,"../assets/500-illustration.svg":q,"../assets/banner-main.svg":B,"../assets/carousel-1.jpg":x,"../assets/carousel-2.jpg":U,"../assets/carousel-3.jpg":S,"../assets/icon-business.svg":I,"../assets/icon-canteen.svg":y,"../assets/icon-catering.svg":k,"../assets/icon-event.svg":j,"../assets/icon-food.svg":h,"../assets/logo-vcfood-old.svg":f,"../assets/logo-vcfood.svg":p,"../assets/map.png":b,"../assets/news-default.svg":u,"../assets/partner-default.svg":m,"../assets/vc-logo.png":d,"../assets/vue.svg":g})[`../assets/${c}`],import.meta.url).href}return(c,M)=>(o(),a("div",O,[s("div",R,[s("div",T,[s("h1",null,_(i(t)("services.title")),1),s("div",V,_(i(t)("services.slogan")),1)]),s("div",$,[s("h2",null,_(i(t)("services.listTitle")),1),s("div",A,[(o(!0),a(z,null,E(n.value,(e,r)=>(o(),a("div",{class:"service-card animate__animated animate__fadeInUp",key:e.title,style:N({animationDelay:.2+r*.1+"s"})},[s("img",{src:v(e.icon),alt:e.title,class:"service-icon"},null,8,C),s("div",G,_(e.title),1),s("div",H,_(e.desc),1)],4))),128))])])])]))}},X=D(J,[["__scopeId","data-v-b391e784"]]);export{X as default};
