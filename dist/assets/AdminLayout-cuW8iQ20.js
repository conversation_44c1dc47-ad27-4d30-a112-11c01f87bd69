import{N,_ as I}from"./NavBar-OuIYKUdi.js";import{_ as L,b as U,u as B}from"./index-ONkDcv6l.js";import{ak as M,e as j,b as u,j as A,t as J,w as K,al as q,x as o,am as n,aj as r,r as c,ao as d,F as E,n as H,o as O,J as f,u as g}from"./locales-B5peU6Va.js";const R={class:"admin-layout"},V={class:"admin-sider"},z={class:"sider-user-info"},F={class:"user-name"},T={class:"admin-main"},W={class:"admin-content-wrapper"},$={__name:"AdminLayout",setup(D){const{t:m}=M(),v=B(),y=U(),s=j(!1),l=u(()=>y.path.startsWith("/admin/users")?"users":y.path.startsWith("/admin/news")?"news":null),k=u(()=>l.value&&typeof l.value=="string"&&l.value.length>0?[l.value]:[]);function w(t){if(!t)return{};try{const a=t.split(".")[1].replace(/-/g,"+").replace(/_/g,"/"),p=decodeURIComponent(atob(a).split("").map(function(_){return"%"+("00"+_.charCodeAt(0).toString(16)).slice(-2)}).join(""));return JSON.parse(p)}catch{return{}}}const S=u(()=>{const t=localStorage.getItem("token");return t&&w(t).sub||""});u(()=>{const t=localStorage.getItem("token");if(!t)return"";const e=w(t);return JSON.stringify(e,null,2)});function x({key:t}){t==="logout"?s.value=!0:v.push(`/admin/${t}`)}function b(){s.value=!1,localStorage.removeItem("token"),v.push("/admin/login")}function i(){H(()=>{const t=document.querySelector(".admin-content-wrapper"),e=document.querySelector(".admin-sider");t&&e&&(e.style.height=t.offsetHeight+"px")})}return A(()=>{i(),window.addEventListener("resize",i)}),J(()=>{window.removeEventListener("resize",i)}),K(()=>document.querySelector(".admin-content-wrapper")?.offsetHeight,i),(t,e)=>{const a=c("a-menu-item"),p=c("a-menu"),_=c("router-view"),C=c("a-modal");return O(),q(E,null,[o(N),n("div",R,[n("aside",V,[e[3]||(e[3]=n("div",{class:"sider-logo"},[n("img",{src:I,alt:"logo"})],-1)),o(p,{mode:"inline",selectedKeys:k.value,onClick:x},{default:r(()=>[o(a,{key:"news"},{default:r(()=>[f(d(g(m)("admin.newsManage")),1)]),_:1}),o(a,{key:"users"},{default:r(()=>[f(d(g(m)("admin.userManage")),1)]),_:1}),o(a,{key:"logout"},{default:r(()=>[f(d(g(m)("admin.logout")),1)]),_:1})]),_:1},8,["selectedKeys"]),n("div",z,[e[2]||(e[2]=n("span",{class:"user-label"},"當前用戶",-1)),n("span",F,d(S.value),1)])]),n("main",T,[n("div",W,[o(_)])]),o(C,{open:s.value,"onUpdate:open":e[0]||(e[0]=h=>s.value=h),title:"確認登出",onOk:b,onCancel:e[1]||(e[1]=h=>s.value=!1),"ok-text":"確認","cancel-text":"取消"},{default:r(()=>e[4]||(e[4]=[n("span",null,"確定要登出嗎？",-1)])),_:1,__:[4]},8,["open"])])],64)}}},X=L($,[["__scopeId","data-v-ce51e8ad"]]);export{X as default};
