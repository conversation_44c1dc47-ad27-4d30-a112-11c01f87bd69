import{_ as c,b as d}from"./index-ONkDcv6l.js";import{b as _}from"./news-C-lmIvy8.js";import{k as u}from"./marked.esm-DJbTXz-_.js";import{e as m,j as p,al as n,am as t,ar as v,ao as o,o as l}from"./locales-B5peU6Va.js";import"./request-D1ZcfQrQ.js";const h={class:"news-detail-root"},w={class:"news-detail-container animate__animated animate__fadeInUp"},f={class:"news-detail-header"},g={class:"news-detail-date"},k=["innerHTML"],N=["src","alt"],y={__name:"NewsDetail",setup(D){const i=d(),e=m(null);p(async()=>{const a=i.params.id;try{const s=await _(a);e.value=s.data||null}catch{e.value=null}});function r(a){return u.parse(a||"")}return(a,s)=>(l(),n("div",h,[t("div",w,[t("div",f,[t("h1",null,o(e.value?.titleZh),1),t("div",g,o(e.value?.createdAt?.substring(0,10)),1)]),t("div",{class:"news-detail-content",innerHTML:r(e.value?.contentZh)},null,8,k),e.value?.imageUrl?(l(),n("img",{key:0,src:e.value.imageUrl,class:"news-detail-img",alt:e.value.titleZh},null,8,N)):v("",!0)])]))}},Z=c(y,[["__scopeId","data-v-e6877927"]]);export{Z as default};
