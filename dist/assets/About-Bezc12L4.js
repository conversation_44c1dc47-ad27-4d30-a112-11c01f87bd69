import{_ as d}from"./index-ONkDcv6l.js";import{ak as u,b as r,al as n,am as a,ao as s,u as e,F as v,an as b,o as i}from"./locales-B5peU6Va.js";const m={class:"page-root"},p={class:"about-container animate__animated animate__fadeInUp"},h={class:"about-header"},f={class:"slogan"},x={class:"about-section"},g={class:"about-section vision animate__animated animate__fadeInUp animate__delay-1s"},I={class:"vision-card"},k={class:"vision-text"},y={class:"about-section values animate__animated animate__fadeInUp animate__delay-2s"},T={class:"core-values"},B={class:"core-value-icon"},U={class:"core-value-title"},A={class:"core-value-text"},D={__name:"About",setup(F){const{t,tm:_}=u(),l=r(()=>_("about.values"));return(V,c)=>(i(),n("div",m,[a("div",p,[a("div",h,[a("h1",null,s(e(t)("about.title")),1),a("div",f,s(e(t)("about.slogan")),1)]),a("div",x,[a("h2",null,s(e(t)("about.aboutTitle")),1),a("p",null,s(e(t)("about.aboutDesc")),1)]),a("div",g,[a("h3",null,s(e(t)("about.visionTitle")),1),a("div",I,[c[0]||(c[0]=a("span",{class:"vision-icon"},"🌟",-1)),a("span",k,s(e(t)("about.visionText")),1)])]),a("div",y,[a("h3",null,s(e(t)("about.coreTitle")),1),a("div",T,[(i(!0),n(v,null,b(l.value,(o,E)=>(i(),n("div",{class:"core-value-card",key:o.text},[a("div",B,s(o.icon),1),a("div",U,s(o.title),1),a("div",A,s(o.text),1)]))),128))])])])]))}},S=d(D,[["__scopeId","data-v-29eb4682"]]);export{S as default};
