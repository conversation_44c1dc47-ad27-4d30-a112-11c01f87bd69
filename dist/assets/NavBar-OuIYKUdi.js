import{_ as m}from"./index-ONkDcv6l.js";import{ak as b,al as r,o as i,am as a,F as h,an as g,x as _,aj as v,J as p,ao as e,u as s,r as f}from"./locales-B5peU6Va.js";const k=""+new URL("vc-logo-CWH6HCP6.png",import.meta.url).href,x={class:"navbar-banner"},C={class:"navbar-content"},N={class:"nav-links"},B={class:"navbar-actions"},w={__name:"NavBar",setup(H){const{locale:o,t}=b(),u=[{path:"/",label:"nav.home"},{path:"/about",label:"nav.about"},{path:"/services",label:"nav.services"},{path:"/news",label:"nav.news"},{path:"/contact",label:"nav.contact"}];function d(){o.value=o.value==="zh-HK"?"en":"zh-HK"}return(L,l)=>{const c=f("router-link");return i(),r("nav",x,[a("div",C,[l[0]||(l[0]=a("img",{class:"logo-img",src:k,alt:"VCFOOD LOGO"},null,-1)),a("ul",N,[(i(),r(h,null,g(u,n=>a("li",{key:n.path},[_(c,{to:n.path,"active-class":"active"},{default:v(()=>[p(e(s(t)(n.label)),1)]),_:2},1032,["to"])])),64))]),a("div",B,[a("button",{class:"lang-btn",onClick:d},e(s(t)("nav.lang")),1),_(c,{to:"/admin",class:"admin-btn"},{default:v(()=>[p(e(s(t)("nav.admin")),1)]),_:1})])])])}}},F=m(w,[["__scopeId","data-v-e39bd73d"]]);export{F as N,k as _};
