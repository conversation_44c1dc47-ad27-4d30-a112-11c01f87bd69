import{_ as L,a as p,u as b}from"./index-DzBUZktP.js";import{g as M,l as C}from"./user--h38gAPV.js";import{N as I}from"./NavBar-ilJ6hyDA.js";import{F as N}from"./FooterBar-Dt23Dukt.js";import{ak as j,e as m,j as O,al as S,x as a,am as l,ao as w,u as s,aj as r,r as i,F as U,o as V,J as q}from"./locales-BE9hxia1.js";import"./request-Fi8fX_Pe.js";const D="data:image/svg+xml,%3csvg%20width='180'%20height='48'%20viewBox='0%200%20180%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3c!--%20Geometric%20monogram%20'vcfood'%20with%20interlocked%20letters%20--%3e%3c!--%20Letter%20'v'%20-%20left%20part%20of%20interlocked%20design%20--%3e%3cpath%20d='M8%2012%20L16%2032%20L24%2012'%20stroke='%23FF6B35'%20stroke-width='3'%20fill='none'%20stroke-linecap='round'%20stroke-linejoin='round'/%3e%3c!--%20Letter%20'c'%20-%20interlocked%20with%20'v'%20--%3e%3cpath%20d='M32%2020%20Q24%2012%2024%2024%20Q24%2036%2032%2028'%20stroke='%23FF6B35'%20stroke-width='3'%20fill='none'%20stroke-linecap='round'/%3e%3c!--%20Letter%20'f'%20with%20chopsticks%20as%20negative%20space%20--%3e%3cg%3e%3c!--%20Main%20'f'%20structure%20--%3e%3cpath%20d='M40%2036%20L40%2012%20L52%2012%20M40%2022%20L48%2022'%20stroke='%23FF6B35'%20stroke-width='3'%20fill='none'%20stroke-linecap='round'%20stroke-linejoin='round'/%3e%3c!--%20Chopsticks%20formed%20by%20negative%20space%20(white%20lines%20within%20the%20'f')%20--%3e%3cline%20x1='42'%20y1='16'%20x2='46'%20y2='20'%20stroke='white'%20stroke-width='1.5'%20stroke-linecap='round'/%3e%3cline%20x1='42'%20y1='18'%20x2='46'%20y2='22'%20stroke='white'%20stroke-width='1.5'%20stroke-linecap='round'/%3e%3c/g%3e%3c!--%20Letter%20'o'%20-%20first%20--%3e%3ccircle%20cx='64'%20cy='24'%20r='8'%20stroke='%23FF6B35'%20stroke-width='3'%20fill='none'/%3e%3c!--%20Letter%20'o'%20-%20second,%20interlocked%20with%20first%20--%3e%3ccircle%20cx='76'%20cy='24'%20r='8'%20stroke='%23FF6B35'%20stroke-width='3'%20fill='none'/%3e%3c!--%20Letter%20'd'%20-%20completing%20the%20word%20--%3e%3cg%3e%3ccircle%20cx='92'%20cy='24'%20r='8'%20stroke='%23FF6B35'%20stroke-width='3'%20fill='none'/%3e%3cpath%20d='M100%2012%20L100%2036'%20stroke='%23FF6B35'%20stroke-width='3'%20fill='none'%20stroke-linecap='round'/%3e%3c/g%3e%3c!--%20Connecting%20elements%20to%20show%20interlocking%20--%3e%3cpath%20d='M24%2024%20L32%2024'%20stroke='%23FF6B35'%20stroke-width='2'%20fill='none'%20opacity='0.8'/%3e%3cpath%20d='M72%2024%20L76%2024'%20stroke='%23FF6B35'%20stroke-width='2'%20fill='none'%20opacity='0.8'/%3e%3cpath%20d='M84%2024%20L92%2024'%20stroke='%23FF6B35'%20stroke-width='2'%20fill='none'%20opacity='0.8'/%3e%3c!--%20Subtle%20accent%20elements%20--%3e%3ccircle%20cx='110'%20cy='18'%20r='2'%20fill='%23FF6B35'%20opacity='0.6'/%3e%3ccircle%20cx='114'%20cy='30'%20r='1.5'%20fill='%23FF6B35'%20opacity='0.4'/%3e%3c/svg%3e",A={class:"admin-login-root"},G={class:"admin-login-card animate__animated animate__fadeInDown"},Q={class:"captcha-row"},E=["src"],J={__name:"AdminLogin",setup(R){const{t:e}=j(),v=b(),o=m({username:"",password:"",captcha:""}),g=m(""),f=m(""),h=m(!1),_={username:[{required:!0,message:e("login.username")}],password:[{required:!0,message:e("login.password")}],captcha:[{required:!0,message:e("login.captcha")}]};async function d(){try{const t=await M();g.value=t.data.img,f.value=t.data.uuid}catch{p.error(e("login.captcha")+" "+e("error.500"))}}O(d);async function F(){h.value=!0;try{const t=await C({username:o.value.username,password:o.value.password,captcha:o.value.captcha,captchaUuid:f.value});console.log(t),t.data&&t.data.token?(localStorage.setItem("token",t.data.token),p.success(e("login.title")+" Success!"),v.push("/admin/news")):(p.error(t.data||e("login.title")+" Failed!"),d())}catch(t){p.error(t.response?.data||e("login.title")+" Failed!"),d()}finally{h.value=!1}}return(t,n)=>{const k=i("a-input"),u=i("a-form-item"),y=i("a-input-password"),x=i("a-button"),B=i("a-form");return V(),S(U,null,[a(I),l("div",A,[l("div",G,[n[3]||(n[3]=l("img",{class:"logo-main",src:D,alt:"VCFOOD LOGO"},null,-1)),l("h2",null,w(s(e)("login.title")),1),a(B,{model:o.value,rules:_,onFinish:F,layout:"vertical"},{default:r(()=>[a(u,{name:"username",label:s(e)("login.username")},{default:r(()=>[a(k,{value:o.value.username,"onUpdate:value":n[0]||(n[0]=c=>o.value.username=c),placeholder:s(e)("login.username")},null,8,["value","placeholder"])]),_:1},8,["label"]),a(u,{name:"password",label:s(e)("login.password")},{default:r(()=>[a(y,{value:o.value.password,"onUpdate:value":n[1]||(n[1]=c=>o.value.password=c),placeholder:s(e)("login.password")},null,8,["value","placeholder"])]),_:1},8,["label"]),a(u,{name:"captcha",label:s(e)("login.captcha")},{default:r(()=>[l("div",Q,[a(k,{value:o.value.captcha,"onUpdate:value":n[2]||(n[2]=c=>o.value.captcha=c),placeholder:s(e)("login.captcha"),style:{width:"150px"}},null,8,["value","placeholder"]),l("img",{src:g.value,onClick:d,class:"captcha-img",alt:"captcha"},null,8,E)])]),_:1},8,["label"]),a(u,null,{default:r(()=>[a(x,{type:"primary","html-type":"submit",block:"",loading:h.value},{default:r(()=>[q(w(s(e)("login.submit")),1)]),_:1},8,["loading"])]),_:1})]),_:1},8,["model"])])]),a(N)],64)}}},X=L(J,[["__scopeId","data-v-1700aaae"]]);export{X as default};
