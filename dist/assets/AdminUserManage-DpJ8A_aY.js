import{a as h,u as E,b as K,d as R}from"./user--h38gAPV.js";import{_ as T,a as c}from"./index-DzBUZktP.js";import{e as r,b as q,j as G,al as I,am as k,x as s,c as M,ar as y,aj as l,r as u,o as f,J as b,F as H}from"./locales-BE9hxia1.js";import"./request-Fi8fX_Pe.js";const Q={class:"admin-user-root animate__animated animate__fadeInUp"},W={class:"admin-user-header"},X={__name:"AdminUserManage",setup(Y){const U=r([]),N=[{title:"用戶名",dataIndex:"username",key:"username"},{title:"操作",key:"action"}],i=r(!1),t=r({id:null,username:"",password:""}),d=r(!1),v=r(null),o=r({current:1,pageSize:10,total:0});function $(a){if(!a)return{};try{const m=a.split(".")[1].replace(/-/g,"+").replace(/_/g,"/"),_=decodeURIComponent(atob(m).split("").map(function(g){return"%"+("00"+g.charCodeAt(0).toString(16)).slice(-2)}).join(""));return JSON.parse(_)}catch{return{}}}const C=q(()=>{const a=localStorage.getItem("token");return a&&$(a).sub||""});G(async()=>{await p()});async function p(){const a=await h({page:o.value.current,pageSize:o.value.pageSize});a&&a.data&&(U.value=a.data.list||a.data,o.value.total=a.data.total||a.data.length||0)}function j(a,e){(o.value.current!==a||o.value.pageSize!==e)&&(o.value.current=a,o.value.pageSize=e,p())}function A(a,e){o.value.current=1,o.value.pageSize=e,p()}function B(a){t.value={id:a.id,username:a.username,password:""},i.value=!0}async function V(){if(!t.value.username||!t.value.password){c.error("請填寫用戶名和密碼");return}t.value.id?(await E({id:t.value.id,password:t.value.password}),c.success("密碼已更新")):(await K({username:t.value.username,password:t.value.password}),c.success("已新增用戶")),await p(),S()}function S(){i.value=!1,t.value={id:null,username:"",password:""}}function D(a){if(a.username==="admin"){c.warning("admin用戶不能被刪除");return}v.value=a.id,d.value=!0}async function F(){d.value=!1,v.value&&(await R(v.value),c.success("已刪除"),await p(),v.value=null)}return(a,e)=>{const m=u("a-button"),_=u("a-table"),g=u("a-pagination"),J=u("a-input"),x=u("a-form-item"),O=u("a-input-password"),P=u("a-form"),z=u("a-modal");return f(),I("div",Q,[k("div",W,[e[7]||(e[7]=k("h2",null,"用戶管理",-1)),C.value==="admin"?(f(),M(m,{key:0,type:"primary",onClick:e[0]||(e[0]=n=>i.value=!0),class:"main-btn"},{default:l(()=>e[6]||(e[6]=[b("新增用戶")])),_:1,__:[6]})):y("",!0)]),s(_,{dataSource:U.value,columns:N,rowKey:"id",bordered:"",class:"admin-user-table",pagination:!1},{bodyCell:l(({column:n,record:w})=>[n.key==="action"&&C.value==="admin"?(f(),I(H,{key:0},[s(m,{type:"link",onClick:L=>B(w)},{default:l(()=>e[8]||(e[8]=[b("修改密碼")])),_:2,__:[8]},1032,["onClick"]),w.username!=="admin"?(f(),M(m,{key:0,type:"link",danger:"",onClick:L=>D(w)},{default:l(()=>e[9]||(e[9]=[b("刪除")])),_:2,__:[9]},1032,["onClick"])):y("",!0)],64)):y("",!0)]),_:1},8,["dataSource"]),s(g,{class:"admin-user-pagination",current:o.value.current,pageSize:o.value.pageSize,total:o.value.total,onChange:j,"show-size-changer":"",onShowSizeChange:A,"show-total":n=>`共 ${n} 條`,style:{"margin-top":"24px","align-self":"flex-end"}},null,8,["current","pageSize","total","show-total"]),s(z,{open:i.value,"onUpdate:open":e[3]||(e[3]=n=>i.value=n),title:"新增/修改用戶",onOk:V,onCancel:S,width:"400px","ok-text":"確認","cancel-text":"取消"},{default:l(()=>[s(P,{layout:"vertical"},{default:l(()=>[s(x,{label:"用戶名"},{default:l(()=>[s(J,{value:t.value.username,"onUpdate:value":e[1]||(e[1]=n=>t.value.username=n),disabled:!!t.value.id},null,8,["value","disabled"])]),_:1}),s(x,{label:"密碼"},{default:l(()=>[s(O,{value:t.value.password,"onUpdate:value":e[2]||(e[2]=n=>t.value.password=n)},null,8,["value"])]),_:1})]),_:1})]),_:1},8,["open"]),s(z,{open:d.value,"onUpdate:open":e[4]||(e[4]=n=>d.value=n),title:"確認刪除",onOk:F,onCancel:e[5]||(e[5]=n=>d.value=!1),"ok-text":"確認","cancel-text":"取消"},{default:l(()=>e[10]||(e[10]=[k("span",null,"確定要刪除此用戶嗎？",-1)])),_:1,__:[10]},8,["open"])])}}},ne=T(X,[["__scopeId","data-v-d5874bce"]]);export{ne as default};
