# 双语网站部署指南

## 问题描述
本地运行正常，但打包部署时双语信息无法正常显示。

## 解决方案

### 1. 已修复的问题

#### ✅ vite.config.js 配置优化
- 移除了错误的 `pathResolve` 函数
- 添加了更稳定的构建配置
- 确保语言文件正确打包到独立的 chunk

#### ✅ i18n 配置增强
- 添加了浏览器语言检测
- 增加了错误处理和回退机制
- 添加了静默警告配置

#### ✅ 语言切换持久化
- 添加了本地存储功能
- 页面刷新后保持语言选择

### 2. 部署检查清单

#### 构建前检查
```bash
# 1. 确保依赖正确安装
npm install

# 2. 检查语言文件是否存在
ls src/i18n/
# 应该看到: en.json, zh-HK.json, index.js

# 3. 运行构建
npm run build

# 4. 运行构建检查脚本
node check-build.js
```

#### 部署后检查
```bash
# 1. 检查静态文件服务器配置
# 确保支持 SPA 路由 (history mode)

# 2. 检查 MIME 类型
# 确保 .js 文件正确设置为 application/javascript

# 3. 检查 CORS 设置 (如果需要)
```

### 3. 常见部署问题及解决方案

#### 问题 1: 语言文件加载失败
**症状**: 页面显示翻译键而不是实际文本
**解决方案**:
- 检查网络面板，确保 `locales-*.js` 文件正确加载
- 确保服务器正确设置 JavaScript 文件的 MIME 类型

#### 问题 2: 路径问题
**症状**: 资源文件 404 错误
**解决方案**:
- 确认 `vite.config.js` 中的 `base` 配置正确
- 对于子目录部署，设置 `base: '/your-subdirectory/'`

#### 问题 3: 缓存问题
**症状**: 更新后仍显示旧内容
**解决方案**:
- 清除浏览器缓存
- 设置适当的缓存头
- 使用版本化的文件名（Vite 默认已启用）

### 4. 推荐的部署配置

#### Nginx 配置示例
```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /path/to/dist;
    index index.html;

    # 支持 SPA 路由
    location / {
        try_files $uri $uri/ /index.html;
    }

    # 静态资源缓存
    location /assets/ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # 确保正确的 MIME 类型
    location ~* \.js$ {
        add_header Content-Type application/javascript;
    }
}
```

#### Apache .htaccess 配置示例
```apache
RewriteEngine On
RewriteBase /

# 支持 SPA 路由
RewriteRule ^index\.html$ - [L]
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule . /index.html [L]

# 静态资源缓存
<FilesMatch "\.(js|css|png|jpg|jpeg|gif|svg|woff|woff2)$">
    ExpiresActive On
    ExpiresDefault "access plus 1 year"
</FilesMatch>
```

### 5. 验证部署成功

1. **功能测试**:
   - 访问网站首页
   - 点击语言切换按钮
   - 刷新页面确认语言保持
   - 测试所有页面的翻译

2. **技术检查**:
   - 打开浏览器开发者工具
   - 检查网络面板确保所有资源正确加载
   - 检查控制台确保没有错误

3. **性能检查**:
   - 使用 Lighthouse 检查性能
   - 确保语言文件正确缓存

### 6. 故障排除

如果部署后仍有问题，请检查：

1. **浏览器控制台错误**
2. **网络面板中的失败请求**
3. **服务器日志**
4. **语言文件是否正确包含在构建输出中**

### 7. 联系支持

如果问题仍然存在，请提供：
- 浏览器控制台错误截图
- 网络面板截图
- 部署环境信息
- 服务器配置文件
